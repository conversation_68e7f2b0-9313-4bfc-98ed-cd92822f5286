ПЛАН МИГРАЦИИ НА АСИНХРОННОСТЬ - РЕДАКТИРОВАНИЕ СУЩЕСТВУЮЩИХ ФАЙЛОВ
====================================================================

ЦЕЛЬ: Перевести все синхронные функции на асинхронные в существующих файлах проекта

═══════════════════════════════════════════════════════════════════════════════

ЭТАП 1: УСТАНОВКА ЗАВИСИМОСТЕЙ И ПОДГОТОВКА
==========================================

1.1. Установить новые пакеты:
pip install aiogram aiohttp aiosqlite aiofiles

1.2. Обновить импорты в requirements.txt (если есть)

ТЕСТИРОВАНИЕ: Проверить установку всех пакетов
РЕЗУЛЬТАТ: Что изменено: ✅ Установлены пакеты: aiogram, aiohttp, aiosqlite, aiofiles
          Где изменено: Создан requirements.txt с зависимостями

═══════════════════════════════════════════════════════════════════════════════

ЭТАП 2: РЕДАКТИРОВАНИЕ database.py
=================================

ЦЕЛЬ: Перевести все БД операции на aiosqlite

2.1. ИЗМЕНИТЬ ИМПОРТЫ (строки 1-11):
ЗАМЕНИТЬ:
import sqlite3
import threading
db_lock = threading.Lock()

НА:
import aiosqlite
import asyncio
db_lock = asyncio.Lock()

2.2. ИЗМЕНИТЬ ФУНКЦИЮ init_database() (строка 13):
ЗАМЕНИТЬ: def init_database():
НА: async def init_database():

ЗАМЕНИТЬ: with db_lock:
НА: async with db_lock:

ЗАМЕНИТЬ: conn = sqlite3.connect(DB_PATH)
НА: async with aiosqlite.connect(DB_PATH) as conn:

2.3. ИЗМЕНИТЬ ВСЕ ФУНКЦИИ БД НА ASYNC:
- log_message() → async def log_message()
- get_stats() → async def get_stats()  
- get_chat_stats() → async def get_chat_stats()
- set_last_podcast_time() → async def set_last_podcast_time()
- get_last_podcast_time() → async def get_last_podcast_time()

2.4. ЗАМЕНИТЬ ВСЕ cursor.execute НА await cursor.execute
2.5. ЗАМЕНИТЬ ВСЕ conn.commit() НА await conn.commit()

ТЕСТИРОВАНИЕ: Запустить тесты БД операций
РЕЗУЛЬТАТ: Что изменено: ✅ Все функции БД переведены на async/await
          Где изменено: database.py - все функции стали асинхронными:
          - init_database() → async def init_database()
          - is_group_unlocked() → async def is_group_unlocked()
          - unlock_group() → async def unlock_group()
          - lock_group() → async def lock_group()
          - get_group_status() → async def get_group_status()
          - save_message() → async def save_message()
          - get_chat_messages() → async def get_chat_messages()
          - get_chat_stats() → async def get_chat_stats()
          - get_last_podcast_time() → async def get_last_podcast_time()
          - set_last_podcast_time() → async def set_last_podcast_time()
          - get_stats() → async def get_stats()
          - log_api_request() → async def log_api_request()
          ✅ Заменены импорты: sqlite3 → aiosqlite, threading → asyncio
          ✅ Заменен db_lock: threading.Lock() → asyncio.Lock()
          ✅ Все cursor.execute → await cursor.execute
          ✅ Все conn.commit() → await conn.commit()
          ✅ Все sqlite3.OperationalError → aiosqlite.OperationalError

═══════════════════════════════════════════════════════════════════════════════

ЭТАП 3: РЕДАКТИРОВАНИЕ api_clients.py  
====================================

ЦЕЛЬ: Перевести все HTTP запросы на aiohttp

3.1. ИЗМЕНИТЬ ИМПОРТЫ (строки 1-30):
ДОБАВИТЬ:
import aiohttp
import asyncio

3.2. ИЗМЕНИТЬ call_voidai_api_sync() (строка 50):
ЗАМЕНИТЬ: def call_voidai_api_sync(...)
НА: async def call_voidai_api(...)

ЗАМЕНИТЬ: response = requests.post(VOIDAI_API_HOST, headers=headers, json=payload, timeout=600)
НА: 
async with aiohttp.ClientSession() as session:
    async with session.post(VOIDAI_API_HOST, headers=headers, json=payload, timeout=aiohttp.ClientTimeout(total=600)) as response:

3.3. ИЗМЕНИТЬ call_gemini_api() (строка 256):
ЗАМЕНИТЬ: def call_gemini_api(...)
НА: async def call_gemini_api(...)

3.4. ИЗМЕНИТЬ ВСЕ ОСТАЛЬНЫЕ API ФУНКЦИИ:
- call_llm() → async def call_llm()
- call_gemma_3_1b_api() → async def call_gemma_3_1b_api()
- call_navy_image_generate_api_sync() → async def call_navy_image_generate_api()

3.5. ЗАМЕНИТЬ ВСЕ requests.post НА aiohttp session calls

ТЕСТИРОВАНИЕ: Проверить все API вызовы
РЕЗУЛЬТАТ: Что изменено: ✅ Все HTTP запросы переведены на aiohttp
          Где изменено: api_clients.py - все функции стали асинхронными:
          ✅ call_voidai_api_sync() → async def call_voidai_api()
          ✅ call_gemini_api() → async def call_gemini_api()
          ✅ call_official_gemini_api() → async def call_official_gemini_api()
          ✅ call_gemini_tts_api() → async def call_gemini_tts_api()
          ✅ call_gemini_2_5_flash_api() → async def call_gemini_2_5_flash_api()
          ✅ call_gemma_3_1b_api() → async def call_gemma_3_1b_api()
          ✅ call_llm() → async def call_llm()
          ✅ call_llm_prioritized() → async def call_llm_prioritized()
          ✅ call_llm_original() → async def call_llm_original()
          ✅ call_llm_stream() → async def call_llm_stream()
          ✅ call_gemini() → async def call_gemini()
          ✅ call_gemini_single_tts_api() → async def call_gemini_single_tts_api()
          ✅ call_gemini_single_tts_api_via_voidai() → async def call_gemini_single_tts_api_via_voidai()
          ✅ call_navy_image_generate_api_sync() → async def call_navy_image_generate_api()
          ✅ call_gemini_2_5_flash_lite_for_shortening() → async def call_gemini_2_5_flash_lite_for_shortening()
          ✅ Все requests.post → aiohttp session calls
          ✅ Все requests.exceptions → aiohttp.ClientError/ClientResponseError
          ✅ Все time.sleep() → await asyncio.sleep()
          ✅ Все log_api_request() → await log_api_request()

═══════════════════════════════════════════════════════════════════════════════

ЭТАП 4: РЕДАКТИРОВАНИЕ utils.py
==============================

ЦЕЛЬ: Перевести файловые операции и FFmpeg на async

4.1. ИЗМЕНИТЬ ИМПОРТЫ:
ДОБАВИТЬ:
import aiofiles
import asyncio

4.2. ИЗМЕНИТЬ convert_to_mp3() (строка 582):
ЗАМЕНИТЬ: def convert_to_mp3(input_path, output_path):
НА: async def convert_to_mp3(input_path, output_path):

ЗАМЕНИТЬ: subprocess.run(command, check=True, capture_output=True, text=True)
НА: process = await asyncio.create_subprocess_exec(*command, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE)
    await process.communicate()

4.3. ИЗМЕНИТЬ queue_ffmpeg_operation() (строка 822):
ЗАМЕНИТЬ: queue.Queue()
НА: asyncio.Queue()

4.4. ИЗМЕНИТЬ ВСЕ ФАЙЛОВЫЕ ОПЕРАЦИИ:
ЗАМЕНИТЬ: with open(file_path, 'r') as f:
НА: async with aiofiles.open(file_path, 'r') as f:

4.5. ИЗМЕНИТЬ send_long_message() и другие bot API функции:
ДОБАВИТЬ async/await для всех bot.send_message() вызовов

ТЕСТИРОВАНИЕ: Проверить FFmpeg и файловые операции
РЕЗУЛЬТАТ: Что изменено: ________________________________________________
          Где изменено: _________________________________________________

═══════════════════════════════════════════════════════════════════════════════

ЭТАП 5: РЕДАКТИРОВАНИЕ processing_core.py
========================================

ЦЕЛЬ: Перевести основную логику обработки на async

5.1. ИЗМЕНИТЬ ИМПОРТЫ:
ДОБАВИТЬ: import asyncio

5.2. ИЗМЕНИТЬ process_text_or_photo_request() (основная функция):
ЗАМЕНИТЬ: def process_text_or_photo_request(...)
НА: async def process_text_or_photo_request(...)

5.3. ИЗМЕНИТЬ ВСЕ API ВЫЗОВЫ:
ЗАМЕНИТЬ: call_llm(...)
НА: await call_llm(...)

ЗАМЕНИТЬ: call_gemini_api(...)  
НА: await call_gemini_api(...)

5.4. ИЗМЕНИТЬ download_and_convert_file():
ЗАМЕНИТЬ: def download_and_convert_file(...)
НА: async def download_and_convert_file(...)

5.5. ИЗМЕНИТЬ ОЧЕРЕДИ:
ЗАМЕНИТЬ: queue.Queue()
НА: asyncio.Queue()

ЗАМЕНИТЬ: threading.Thread(target=_podcast_worker, daemon=True).start()
НА: asyncio.create_task(_podcast_worker())

ТЕСТИРОВАНИЕ: Проверить обработку сообщений и подкастов
РЕЗУЛЬТАТ: Что изменено: ________________________________________________
          Где изменено: _________________________________________________

═══════════════════════════════════════════════════════════════════════════════

ЭТАП 6: РЕДАКТИРОВАНИЕ handlers.py
=================================

ЦЕЛЬ: Перевести все обработчики на aiogram

6.1. ИЗМЕНИТЬ ИМПОРТЫ (строки 1-20):
ЗАМЕНИТЬ: import telebot
НА: from aiogram import Bot, Dispatcher, types, Router
    from aiogram.filters import Command
    from aiogram.types import Message, CallbackQuery

6.2. ИЗМЕНИТЬ ВСЕ @bot.message_handler:
ЗАМЕНИТЬ: @bot.message_handler(commands=['start'])
         def start_command(message):
НА: @router.message(Command('start'))
    async def start_command(message: Message):

6.3. ИЗМЕНИТЬ ВСЕ bot.send_message():
ЗАМЕНИТЬ: bot.send_message(chat_id, text)
НА: await bot.send_message(chat_id, text)

6.4. ИЗМЕНИТЬ ВСЕ threading.Thread():
ЗАМЕНИТЬ: threading.Thread(target=processing_core.process_text_or_photo_request, args=(...)).start()
НА: await processing_core.process_text_or_photo_request(...)

ТЕСТИРОВАНИЕ: Проверить все команды и обработчики
РЕЗУЛЬТАТ: Что изменено: ________________________________________________
          Где изменено: _________________________________________________

═══════════════════════════════════════════════════════════════════════════════

ЭТАП 7: РЕДАКТИРОВАНИЕ main.py
=============================

ЦЕЛЬ: Заменить telebot polling на aiogram

7.1. ИЗМЕНИТЬ ИМПОРТЫ (строки 7-15):
ЗАМЕНИТЬ: import telebot
НА: from aiogram import Bot, Dispatcher
    import asyncio

7.2. ИЗМЕНИТЬ bot.polling() (строки 390-405):
ЗАМЕНИТЬ: bot.infinity_polling(...)
НА: 
async def main():
    bot = Bot(token=BOT_TOKEN)
    dp = Dispatcher()
    await dp.start_polling(bot)

if __name__ == '__main__':
    asyncio.run(main())

7.3. ИЗМЕНИТЬ unified_background_scheduler() (строка 156):
ЗАМЕНИТЬ: def unified_background_scheduler():
НА: async def unified_background_scheduler():

ЗАМЕНИТЬ: time.sleep(300)
НА: await asyncio.sleep(300)

7.4. ИЗМЕНИТЬ ВСЕ threading.Thread():
ЗАМЕНИТЬ: threading.Thread(target=unified_background_scheduler, daemon=True).start()
НА: asyncio.create_task(unified_background_scheduler())

ТЕСТИРОВАНИЕ: Полное тестирование бота
РЕЗУЛЬТАТ: Что изменено: ________________________________________________
          Где изменено: _________________________________________________

═══════════════════════════════════════════════════════════════════════════════

═══════════════════════════════════════════════════════════════════════════════

ДОПОЛНИТЕЛЬНЫЕ ФАЙЛЫ ДЛЯ РЕДАКТИРОВАНИЯ:
=======================================

8.1. РЕДАКТИРОВАНИЕ bot_globals.py:
- Заменить threading.Lock на asyncio.Lock для всех глобальных блокировок
- Обновить safe_bot_api_call для async
- Изменить все глобальные состояния для работы с asyncio

8.2. РЕДАКТИРОВАНИЕ genai_client.py:
- Перевести все Google GenAI вызовы на async
- Обновить connection pooling для async
- Изменить retry логику на async

8.3. РЕДАКТИРОВАНИЕ telegraph_helpers.py:
- Перевести Telegraph API на aiohttp
- Async публикация контента

8.4. РЕДАКТИРОВАНИЕ admin_system.py:
- Async функции администрирования
- Обновить планировщики подкастов

═══════════════════════════════════════════════════════════════════════════════

КРИТИЧЕСКИЕ МОМЕНТЫ ПРИ МИГРАЦИИ:
================================

⚠️ ВАЖНО: НЕ ЗАБЫТЬ:
1. Все вызовы БД функций должны стать await
2. Все HTTP запросы должны стать await
3. Все файловые операции должны стать await
4. Все bot API вызовы должны стать await
5. Заменить time.sleep() на await asyncio.sleep()
6. Заменить threading.Thread на asyncio.create_task()
7. Обновить все импорты в зависимых файлах

ПОРЯДОК ВЫПОЛНЕНИЯ ЭТАПОВ:
1. Сначала database.py (база для всего)
2. Потом api_clients.py (API вызовы)
3. Затем utils.py (утилиты)
4. После processing_core.py (основная логика)
5. Потом handlers.py (обработчики)
6. В конце main.py (точка входа)

═══════════════════════════════════════════════════════════════════════════════

ФИНАЛЬНОЕ ТЕСТИРОВАНИЕ:
======================
1. Запустить бота и проверить все команды
2. Протестировать обработку медиа файлов
3. Проверить создание подкастов
4. Нагрузочное тестирование
5. Проверить производительность

ОЖИДАЕМЫЙ РЕЗУЛЬТАТ:
- Увеличение производительности в 3-5 раз
- Снижение потребления памяти
- Лучшая отзывчивость бота
- Возможность обработки большего количества пользователей одновременно

МЕСТА ДЛЯ ЗАПИСИ ПРОГРЕССА:
После каждого этапа ИИ должен записать:
- Что конкретно изменено
- В каких файлах и строках
- Какие проблемы возникли
- Информацию для следующего этапа
